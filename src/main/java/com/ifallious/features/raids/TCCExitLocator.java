package com.ifallious.features.raids;

import com.ifallious.Wynnutils;
import com.ifallious.utils.ErrorReporter;
import com.ifallious.utils.Tick;
import com.ifallious.utils.Utils;
import com.ifallious.utils.config.ConfigManager;
import gg.essential.universal.UMinecraft;
import gg.essential.universal.wrappers.UPlayer;
import net.minecraft.entity.Entity;
import net.minecraft.entity.decoration.DisplayEntity;
import net.minecraft.sound.SoundCategory;
import net.minecraft.sound.SoundEvents;
import net.minecraft.world.World;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Objects;

public class TCCExitLocator {
    public TCCExitLocator() {
        try {
            Thread thread = new Thread(new ExitThread());
            thread.start();
        } catch (Exception e) {
            Wynnutils.LOGGER.error("TCCExitLocator thread creation failed", e);
        }
    }
    private class ExitThread implements Runnable {
        private long lastMessage = 0;
        @Override
        public void run() {
            while (true) {
                try {
                    World world = UMinecraft.getWorld();
                    if (world == null) {
                        Thread.sleep(1000);
                        continue;
                    }
                    Iterable<Entity> entities = Objects.requireNonNull(UMinecraft.getWorld()).getEntities();
                    for (Entity entity : entities) {
                        try {
                            if (entity instanceof DisplayEntity.TextDisplayEntity textEntity) {
                                String text = textEntity.getText().getString();
                                if (text.contains("EXIT") && Boolean.TRUE.equals(ConfigManager.getFeature("tccExitLocator")) && System.currentTimeMillis() - lastMessage > 60000) {
                                    try {
                                        Tick.schedule(1, () -> {
                                            try {
                                                Utils.simulateChat("§c§lExit: found at: [" + entity.getBlockX() + " " + entity.getBlockY() + " " + entity.getBlockZ() + "]");
                                                UPlayer.getPlayer().playSoundToPlayer(SoundEvents.BLOCK_AMETHYST_BLOCK_BREAK, SoundCategory.MASTER, 1.0f, 1.0f);
                                            } catch (Exception e) {
                                                Wynnutils.LOGGER.error("Error while sending exit message", e);
                                            }
                                        });
                                    } catch (Exception e) {
                                        Wynnutils.LOGGER.error("Error while sending exit message", e);
                                    }
                                    Thread.sleep(60000);
                                    break;
                                }
                            }
                        } catch (Exception e) {
                            Wynnutils.LOGGER.error("Error while processing entity", e);
                        }
                    }
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Wynnutils.LOGGER.error("TCCExitLocator thread interrupted", e);
                    break;
                } catch (Exception e) {
                    Wynnutils.LOGGER.error("Error while running TCCExitLocator thread", e);
                    try {
                        Thread.sleep(5000); // Wait before retrying
                    } catch (InterruptedException ie) {
                        break;
                    }
                }
            }
        }
    }
}
