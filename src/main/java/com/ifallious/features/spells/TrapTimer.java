package com.ifallious.features.spells;

import com.ifallious.Wynnutils;
import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.TickEvent;
import com.ifallious.utils.ErrorReporter;
import com.ifallious.utils.SpellmacroUtils;
import com.ifallious.utils.Tick;
import com.ifallious.utils.Utils;
import com.ifallious.utils.config.ConfigManager;
import com.ifallious.utils.wynntils.StatusEffect;
import com.ifallious.utils.wynntils.StatusEffectUtils;
import gg.essential.universal.UChat;
import gg.essential.universal.UMinecraft;
import gg.essential.universal.wrappers.UPlayer;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.entity.Entity;
import net.minecraft.entity.decoration.DisplayEntity;
import net.minecraft.sound.SoundCategory;
import net.minecraft.sound.SoundEvents;
import net.minecraft.world.World;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Arrays;
import java.util.Objects;

public class TrapTimer {
    public TrapTimer() {
        GlobalEventBus.subscribe(this);
    }
    @EventHandler
    public void onTick(TickEvent event) {
        try {
            if (Boolean.TRUE.equals(ConfigManager.getFeature("trapTimer"))) {
                World world = UMinecraft.getWorld();
                if (world != null) {
                    Iterable<Entity> entities = Objects.requireNonNull(UMinecraft.getWorld()).getEntities();
                    for (Entity entity : entities) {
                        try {
                            if (entity instanceof DisplayEntity.TextDisplayEntity textEntity) {
                                String text = textEntity.getText().getString();
                                if (text.contains("Arming")) {
                                    try {
                                        UChat.chat("Trap in 30 seconds");
                                    } catch (Exception e) {
                                        Wynnutils.LOGGER.error("TrapTimer chat message failed", e);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            Wynnutils.LOGGER.error("TrapTimer entity processing failed", e);
                        }
                    }
                }
            }
        } catch (Exception e) {
            Wynnutils.LOGGER.error("TrapTimer tick event failed", e);
        }
    }
}
